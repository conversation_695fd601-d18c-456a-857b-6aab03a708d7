import {useEffect} from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {setCurrentAgentId} from '@/regions/staff/agent';
import {useCurrentUser} from '@/regions/user/currentUser';
import {MCPSquareLink} from '@/links/mcp';
import SvgMcpPlaygroundWorkspaceBtn from '@/assets/mcp/McpPlaygroundWorkspaceBtn';
import {HistoryPanel} from './HistoryPanel';
import AgentPanel from './AgentPanel';

const Container = styled.div`
    height: 100vh;
    width: 100%;
    overflow: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const Content = styled.div`
    display: flex;
    padding: 80px 44px 24px 44px;
    flex-direction: column;
    gap: 40px;
    overflow: hidden;
`;

const HiText = styled.div`
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 20px;
    line-height: 38px;
    letter-spacing: 0px;
`;

const NameText = styled.div`
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 34px;
    line-height: 38px;
    letter-spacing: 0px;
    background: linear-gradient(
        262.56deg,
        #895feb 5.77%,
        #3258f0 43.42%,
        #040660 79.59%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
`;

const StaffWorkspace = () => {
    const userInfo = useCurrentUser();

    useEffect(
        () => {
            setCurrentAgentId(0);
        },
        []
    );

    return (
        <Container>
            <Content>
                <Flex align="baseline" justify="space-between">
                    <Flex align="baseline" gap={12}>
                        <NameText>{userInfo?.chineseName}</NameText>
                        <HiText>欢迎来到百度研发数字员工工作台！</HiText>
                    </Flex>
                    <Flex a>
                        <a
                            href={MCPSquareLink.toUrl()}
                            target="_blank"
                            rel="noreferrer"
                        >
                            <SvgMcpPlaygroundWorkspaceBtn />
                        </a>
                    </Flex>
                </Flex>
                <AgentPanel />
                <HistoryPanel />
            </Content>
        </Container>
    );
};

export default StaffWorkspace;
