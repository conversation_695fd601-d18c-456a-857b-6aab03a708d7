import styled from '@emotion/styled';
import {MCPSquareLink} from '@/links/mcp';
import SvgMcpPlaygroundHomeBtn from '@/assets/mcp/McpPlaygroundHomeBtn';

const StyledLink = styled.a`
    display: flex;
    align-items: center;
    margin-right: 28px;
    height: 32px;
`;

export const MCPPlaygroundButton = () => {
    return (
        <StyledLink href={MCPSquareLink.toUrl()} target="_blank" rel="noreferrer">
            <SvgMcpPlaygroundHomeBtn />
        </StyledLink>
    );
};
