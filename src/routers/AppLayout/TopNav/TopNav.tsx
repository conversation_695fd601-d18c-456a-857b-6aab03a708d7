import {homeLinkCss, IconHome, TopNavigation} from '@baidu/devops-components';
import {useRouteBreadcrumbItems, useRouteDocumentTitle} from '@panda-design/router';
import {Breadcrumb} from 'antd';
import {Link, useParams} from 'react-router-dom';
import styled from '@emotion/styled';
import {marginLeft} from '@panda-design/components';
import {useDocumentTitle} from 'huse';
import {css} from '@emotion/css';
import {routes} from '@/routers/comatestack';
import {DevTools} from '@/design/Dev/DevTools';
import {useProject} from '@/regions/project/project';
import {useLabelParams} from '@/hooks/label/useLabelParams';
import {useDmProject} from '@/regions/projectLabelTask/dmProject';
import {WorkspaceListLink} from '@/links';
import {CreateButton} from './CreateButton';
import {MCPPlaygroundButton} from './McpPlaygroundButton';
import useRenderAvatarDropdown from './useRenderAvatarDropdown';

const Container = styled.div`
    display: flex;
    align-items: center;
    height: 100%;
    flex: 1;
`;

const breadcrumbCss = css`
    margin-left: 10px !important;

    .panda-link{
        display: block !important;
        max-width: 300px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
`;

const Right = styled.div`
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 8px;
    height: 47px;
`;

const useDocumentContext = () => {
    const {projectUuid} = useParams();
    const project = useProject(projectUuid);
    const {labelStudioProjectId} = useLabelParams();
    const dmProject = useDmProject(labelStudioProjectId);
    return {projectName: project?.name, labelName: dmProject?.title};
};

const TopNav = () => {
    const context = useDocumentContext();
    const documentTitle = useRouteDocumentTitle(routes, context);
    useDocumentTitle(documentTitle ? `ComateStack: ${documentTitle}` : 'ComateStack');
    useRenderAvatarDropdown();
    const breadcrumbItems = useRouteBreadcrumbItems(routes);
    return (
        <Container>
            <TopNavigation
                renderHomeLink={() => (
                    <>
                        <Link to={WorkspaceListLink.toUrl()} className={homeLinkCss}>
                            <IconHome />
                            <span className={marginLeft(4)}>Comate Stack</span>
                        </Link>
                        <DevTools />
                    </>
                )}
                renderBreadcrumb={() => (
                    <Breadcrumb className={breadcrumbCss} items={breadcrumbItems} />
                )}
                renderCreateButton={() => (
                    <Right>
                        <MCPPlaygroundButton />
                        <CreateButton />
                    </Right>
                )}
            />
        </Container>
    );
};

export default TopNav;
